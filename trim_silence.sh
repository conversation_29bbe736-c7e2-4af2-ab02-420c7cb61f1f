#!/bin/bash

# Skript na skrátenie ticha na konci MP3 súborov na 0,5 sekundy
# Autor: Automaticky generovaný skript

echo "Začínam spracovanie 77 MP3 súborov..."
echo "Skracujem ticho na konci na 0,5 sekundy"
echo ""

# Vytvorenie záložného priečinka
backup_dir="backup_$(date +%Y%m%d_%H%M%S)"
mkdir -p "$backup_dir"
echo "Vytvorený záložný priečinok: $backup_dir"
echo ""

# Počítadlo spracovaných súborov
processed=0
total=77

# Spracovanie každého MP3 súboru
for i in {1..77}; do
    input_file="${i}.mp3"
    
    if [ -f "$input_file" ]; then
        echo "Spracovávam súbor: $input_file"
        
        # Vytvorenie zálohy
        cp "$input_file" "$backup_dir/"
        
        # Dočasný súbor pre výstup
        temp_file="temp_${i}.mp3"
        
        # FFmpeg príkaz na odstránenie ticha z konca a pridanie 0,5s ticha
        # 1. Najprv odstránime ticho z konca pomocou reverse + silenceremove + reverse
        # 2. Potom pridáme presne 0,5s ticha na konec
        ffmpeg -i "$input_file" \
               -af "areverse,silenceremove=stop_periods=1:stop_duration=0.1:stop_threshold=-50dB,areverse,apad=pad_dur=0.5" \
               -c:a libmp3lame -b:a 128k \
               "$temp_file" \
               -y -loglevel error
        
        # Kontrola, či sa FFmpeg úspešne dokončil
        if [ $? -eq 0 ]; then
            # Nahradenie pôvodného súboru
            mv "$temp_file" "$input_file"
            processed=$((processed + 1))
            echo "✓ Hotovo: $input_file (${processed}/${total})"
        else
            echo "✗ Chyba pri spracovaní: $input_file"
            # Odstránenie dočasného súboru v prípade chyby
            [ -f "$temp_file" ] && rm "$temp_file"
        fi
        
        echo ""
    else
        echo "⚠ Súbor $input_file neexistuje"
        echo ""
    fi
done

echo "================================================"
echo "Spracovanie dokončené!"
echo "Úspešne spracovaných súborov: ${processed}/${total}"
echo "Zálohy uložené v priečinku: $backup_dir"
echo ""
echo "Ak ste spokojní s výsledkom, môžete záložný priečinok vymazať:"
echo "rm -rf $backup_dir"
